{"info": {"name": "AgriTech Buyer Backend APIs", "description": "Comprehensive API collection for AgriTech Buyer Backend microservices including Authentication, Products, Orders, Profiles, Cart, and Dropdown services.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "auth_base_url", "value": "http://localhost:6001", "type": "string"}, {"key": "product_base_url", "value": "http://localhost:6002", "type": "string"}, {"key": "profile_base_url", "value": "http://localhost:6006", "type": "string"}, {"key": "order_base_url", "value": "http://localhost:6003", "type": "string"}, {"key": "cart_base_url", "value": "http://localhost:6004", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "item": [{"name": "🔐 Authentication Service", "description": "User authentication and authorization endpoints", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/register", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user account"}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.accessToken);", "    pm.collectionVariables.set('refresh_token', response.data.refreshToken);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/login", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "login"]}, "description": "Authenticate user and get access token"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/refresh-token", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "refresh-token"]}, "description": "Refresh access token using refresh token"}}, {"name": "Validate Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{auth_base_url}}/api/auth/validate", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "validate"]}, "description": "Validate current access token"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/forgot-password", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "forgot-password"]}, "description": "Request password reset"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-here\",\n  \"newPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/reset-password", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "reset-password"]}, "description": "Reset password using reset token"}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"SecurePassword123!\",\n  \"newPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/change-password", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "change-password"]}, "description": "Change user password"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{auth_base_url}}/api/auth/logout", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "logout"]}, "description": "Logout user and invalidate token"}}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{auth_base_url}}/health", "host": ["{{auth_base_url}}"], "path": ["health"]}, "description": "Check auth service health"}}]}, {"name": "🌾 Product Service", "description": "Product management, search, categories, and dropdown endpoints", "item": [{"name": "📦 Products", "item": [{"name": "Get Products", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/products?page=1&limit=20&categoryId=&sellerId=&minPrice=&maxPrice=&searchTerm=", "host": ["{{product_base_url}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "categoryId", "value": ""}, {"key": "sellerId", "value": ""}, {"key": "minPrice", "value": ""}, {"key": "maxPrice", "value": ""}, {"key": "searchTerm", "value": ""}]}, "description": "Get list of products with filtering and pagination"}}, {"name": "Get Product by ID", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/products/:productId", "host": ["{{product_base_url}}"], "path": ["api", "products", ":productId"], "variable": [{"key": "productId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get single product by ID"}}]}, {"name": "📂 Categories", "item": [{"name": "Get Categories", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/categories?parentId=", "host": ["{{product_base_url}}"], "path": ["api", "categories"], "query": [{"key": "parentId", "value": ""}]}, "description": "Get categories with optional parent filter"}}, {"name": "Get Category by ID", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/categories/:categoryId", "host": ["{{product_base_url}}"], "path": ["api", "categories", ":categoryId"], "variable": [{"key": "categoryId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get single category by ID"}}]}, {"name": "🔍 Search", "item": [{"name": "Search Products", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/products?query=tomato&productTypes=CROP&categories=&state=Karnataka&city=&minPrice=&maxPrice=&page=1&limit=20&sort=createdAt:desc&includeAggregations=true", "host": ["{{product_base_url}}"], "path": ["api", "search", "products"], "query": [{"key": "query", "value": "tomato"}, {"key": "productTypes", "value": "CROP"}, {"key": "categories", "value": ""}, {"key": "state", "value": "Karnataka"}, {"key": "city", "value": ""}, {"key": "minPrice", "value": ""}, {"key": "maxPrice", "value": ""}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sort", "value": "createdAt:desc"}, {"key": "includeAggregations", "value": "true"}]}, "description": "Advanced product search with Elasticsearch"}}, {"name": "Get Autocomplete Suggestions", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/autocomplete?query=tom&limit=10", "host": ["{{product_base_url}}"], "path": ["api", "search", "autocomplete"], "query": [{"key": "query", "value": "tom"}, {"key": "limit", "value": "10"}]}, "description": "Get autocomplete suggestions"}}, {"name": "Get Similar Products", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/products/:productId/similar?limit=10", "host": ["{{product_base_url}}"], "path": ["api", "search", "products", ":productId", "similar"], "variable": [{"key": "productId", "value": "507f1f77bcf86cd799439011"}], "query": [{"key": "limit", "value": "10"}]}, "description": "Get similar products"}}, {"name": "Get Trending Products", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/trending?limit=20", "host": ["{{product_base_url}}"], "path": ["api", "search", "trending"], "query": [{"key": "limit", "value": "20"}]}, "description": "Get trending products"}}, {"name": "Increment Product View", "request": {"method": "POST", "url": {"raw": "{{product_base_url}}/api/search/products/:productId/view", "host": ["{{product_base_url}}"], "path": ["api", "search", "products", ":productId", "view"], "variable": [{"key": "productId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Increment product view count"}}, {"name": "Elasticsearch Health", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/health/elasticsearch", "host": ["{{product_base_url}}"], "path": ["api", "search", "health", "elasticsearch"]}, "description": "Check Elasticsearch health"}}, {"name": "Search API Documentation", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/docs", "host": ["{{product_base_url}}"], "path": ["api", "search", "docs"]}, "description": "Get search API documentation"}}]}, {"name": "📋 Dropdowns", "item": [{"name": "Product Related", "item": [{"name": "Get Product Types", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/product-types", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "product-types"]}, "description": "Get product types dropdown"}}, {"name": "Get Quality Grades", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/quality-grades", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "quality-grades"]}, "description": "Get quality grades dropdown"}}, {"name": "Get Availability Status", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/availability-status", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "availability-status"]}, "description": "Get availability status dropdown"}}, {"name": "Get Categories Dropdown", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/categories?parentId=null", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "categories"], "query": [{"key": "parentId", "value": "null"}]}, "description": "Get categories dropdown (hierarchical)"}}, {"name": "Get Farming Methods", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/farming-methods", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "farming-methods"]}, "description": "Get farming methods dropdown"}}, {"name": "Get Harvest Seasons", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/harvest-seasons", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "harvest-seasons"]}, "description": "Get harvest seasons dropdown"}}, {"name": "Get Crop Varieties", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/crop-varieties?category=Vegetables", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "crop-varieties"], "query": [{"key": "category", "value": "Vegetables"}]}, "description": "Get crop varieties dropdown"}}]}, {"name": "Location Related", "item": [{"name": "Get Countries", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/countries", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "countries"]}, "description": "Get countries dropdown"}}, {"name": "Get States", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/states?country=India", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "states"], "query": [{"key": "country", "value": "India"}]}, "description": "Get states dropdown by country"}}, {"name": "Get Cities", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/cities?country=India&state=Karnataka", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "cities"], "query": [{"key": "country", "value": "India"}, {"key": "state", "value": "Karnataka"}]}, "description": "Get cities dropdown by country and state"}}, {"name": "<PERSON>", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/pincodes?country=India&state=Karnataka&city=Bangalore", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "pincodes"], "query": [{"key": "country", "value": "India"}, {"key": "state", "value": "Karnataka"}, {"key": "city", "value": "Bangalore"}]}, "description": "Get pincodes dropdown by location"}}]}, {"name": "Reference Data", "item": [{"name": "Get Currencies", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/currencies", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "currencies"]}, "description": "Get currencies dropdown"}}, {"name": "Get Units", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/units", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "units"]}, "description": "Get measurement units dropdown"}}, {"name": "Get Languages", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/languages", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "languages"]}, "description": "Get languages dropdown"}}, {"name": "Get Timezones", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/timezones", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "timezones"]}, "description": "Get timezones dropdown"}}]}, {"name": "Order Related", "item": [{"name": "Get Order Statuses", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/order-statuses", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "order-statuses"]}, "description": "Get order statuses dropdown"}}, {"name": "Get Delivery Methods", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/delivery-methods", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "delivery-methods"]}, "description": "Get delivery methods dropdown"}}, {"name": "Get Payment Methods", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/payment-methods", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "payment-methods"]}, "description": "Get payment methods dropdown"}}]}, {"name": "Profile Related", "item": [{"name": "Get Gender Options", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/gender-options", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "gender-options"]}, "description": "Get gender options dropdown"}}, {"name": "Get Verification Levels", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/verification-levels", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "verification-levels"]}, "description": "Get verification levels dropdown"}}, {"name": "Get Contact Preferences", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/contact-preferences", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "contact-preferences"]}, "description": "Get contact preferences dropdown"}}, {"name": "Get Notification Types", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/notification-types", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "notification-types"]}, "description": "Get notification types dropdown"}}, {"name": "Get Privacy Settings", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/privacy-settings", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "privacy-settings"]}, "description": "Get privacy settings dropdown"}}]}, {"name": "Search & Filter", "item": [{"name": "Get Sort Fields", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/sort-fields", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "sort-fields"]}, "description": "Get sort fields dropdown"}}, {"name": "Get Sort Orders", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/sort-orders", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "sort-orders"]}, "description": "Get sort orders dropdown"}}]}, {"name": "Dropdown API Documentation", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/dropdowns/docs", "host": ["{{product_base_url}}"], "path": ["api", "dropdowns", "docs"]}, "description": "Get dropdown API documentation"}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/health", "host": ["{{product_base_url}}"], "path": ["health"]}, "description": "Check product service health"}}]}, {"name": "👤 Profile Service", "description": "User profile and address management endpoints", "item": [{"name": "👤 Profile Management", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Get current user's profile"}}, {"name": "Get Profile by ID", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/api/profiles/profile/:profileId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile", ":profileId"], "variable": [{"key": "profileId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get profile by ID (public access)"}}, {"name": "Create Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"displayName\": \"John D\",\n  \"dateOfBirth\": \"1990-01-15\",\n  \"gender\": \"male\",\n  \"phoneNumber\": \"+91-9876543210\",\n  \"bio\": \"Passionate about sustainable agriculture\",\n  \"interests\": [\"organic farming\", \"crop rotation\", \"sustainable practices\"]\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Create new user profile"}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"displayName\": \"<PERSON> Do<PERSON>\",\n  \"bio\": \"Updated bio - Passionate about sustainable agriculture and technology\",\n  \"interests\": [\"organic farming\", \"agtech\", \"sustainable practices\"]\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Update user profile"}}, {"name": "Delete Profile", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Delete user profile"}}, {"name": "Search Profiles", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/api/profiles/profiles/search?query=john&page=1&limit=10", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profiles", "search"], "query": [{"key": "query", "value": "john"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Search profiles"}}, {"name": "Update Profile Picture", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"profilePicture\": \"https://example.com/profile-pictures/john-doe.jpg\"\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/profile/picture", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile", "picture"]}, "description": "Update profile picture"}}, {"name": "Verify Profile (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/profile/:userId/verify", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile", ":userId", "verify"], "variable": [{"key": "userId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Verify profile (admin/moderator only)"}}]}, {"name": "🏠 Address Management", "item": [{"name": "Get All Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses"]}, "description": "Get all user addresses"}}, {"name": "Get Address by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get specific address by ID"}}, {"name": "Get Default Address", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/default", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", "default"]}, "description": "Get default address"}}, {"name": "Add Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"street\": \"123 Farm Road\",\n  \"city\": \"Bangalore\",\n  \"state\": \"Karnataka\",\n  \"postalCode\": \"560001\",\n  \"country\": \"India\",\n  \"label\": \"Home\",\n  \"isDefault\": true,\n  \"coordinates\": {\n    \"latitude\": 12.9716,\n    \"longitude\": 77.5946\n  }\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/addresses", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses"]}, "description": "Add new address"}}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"street\": \"456 Updated Farm Road\",\n  \"city\": \"Bangalore\",\n  \"state\": \"Karnataka\",\n  \"postalCode\": \"560002\",\n  \"country\": \"India\",\n  \"label\": \"Home - Updated\"\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Update existing address"}}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Delete address"}}, {"name": "<PERSON> Default Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId/default", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId", "default"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Set address as default"}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/health", "host": ["{{profile_base_url}}"], "path": ["health"]}, "description": "Check profile service health"}}, {"name": "Security Stats", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/security/stats", "host": ["{{profile_base_url}}"], "path": ["security", "stats"]}, "description": "Get security statistics"}}]}, {"name": "📦 Order Service", "description": "Order management and processing endpoints", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"productId\": \"507f1f77bcf86cd799439011\",\n      \"quantity\": 2,\n      \"unitPrice\": 150.00,\n      \"unit\": \"kg\"\n    }\n  ],\n  \"shippingAddress\": {\n    \"street\": \"123 Farm Road\",\n    \"city\": \"Bangalore\",\n    \"state\": \"Karnataka\",\n    \"postalCode\": \"560001\",\n    \"country\": \"India\"\n  },\n  \"deliveryMethod\": \"STANDARD_DELIVERY\",\n  \"paymentMethod\": \"UPI\"\n}"}, "url": {"raw": "{{order_base_url}}/api/orders", "host": ["{{order_base_url}}"], "path": ["api", "orders"]}, "description": "Create a new order"}}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{order_base_url}}/api/orders/:orderId", "host": ["{{order_base_url}}"], "path": ["api", "orders", ":orderId"], "variable": [{"key": "orderId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get order details by ID"}}, {"name": "Get My Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{order_base_url}}/api/orders/user/me?page=1&limit=10&status=", "host": ["{{order_base_url}}"], "path": ["api", "orders", "user", "me"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": ""}]}, "description": "Get current user's orders"}}, {"name": "Update Order Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"CONFIRMED\",\n  \"notes\": \"Order confirmed by seller\"\n}"}, "url": {"raw": "{{order_base_url}}/api/orders/:orderId/status", "host": ["{{order_base_url}}"], "path": ["api", "orders", ":orderId", "status"], "variable": [{"key": "orderId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Update order status"}}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\",\n  \"notes\": \"Customer requested cancellation\"\n}"}, "url": {"raw": "{{order_base_url}}/api/orders/:orderId/cancel", "host": ["{{order_base_url}}"], "path": ["api", "orders", ":orderId", "cancel"], "variable": [{"key": "orderId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Cancel an order"}}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{order_base_url}}/health", "host": ["{{order_base_url}}"], "path": ["health"]}, "description": "Check order service health"}}]}, {"name": "🛒 Cart Service", "description": "Shopping cart management endpoints", "item": [{"name": "Welcome Message", "request": {"method": "GET", "url": {"raw": "{{cart_base_url}}/api", "host": ["{{cart_base_url}}"], "path": ["api"]}, "description": "Get cart service welcome message"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for all requests", "console.log('Making request to:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has success field', function () {", "    if (pm.response.code === 200) {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('success');", "    }", "});"]}}]}